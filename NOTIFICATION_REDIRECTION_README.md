# Notification Redirection Implementation

This document explains the notification redirection functionality that has been implemented in the app.

## Overview

The notification redirection system handles Firebase Cloud Messaging (FCM) notifications and routes users to appropriate screens based on the notification data.

## Implementation Details

### Files Created/Modified

1. **`src/utils/NotificationService.ts`** - Main service for handling notification routing
2. **`src/utils/NotificationTestUtils.ts`** - Test utilities for development
3. **`App.tsx`** - Added Firebase messaging listeners

### Notification Data Structure

The system expects notifications with the following data structure:

```javascript
{
  data: {
    type: "challenge", // or "user"/"profile"
    id: "abc123",      // challengeId or userId
    title: "New Challenge!",
    body: "Check out this challenge."
  }
}
```

### Supported Notification Types

1. **Challenge Notifications** (`type: "challenge"`)
   - Redirects to: `BottomTabsNavigator -> Home -> ChallengeDetails`
   - Requires: `challengeId` in the `id` field

2. **User Profile Notifications** (`type: "user"` or `type: "profile"`)
   - Redirects to: `BottomTabsNavigator -> Home -> OtherUserProfileScreen`
   - Requires: `userId` in the `id` field

3. **Unknown/Empty Notifications**
   - Redirects to: `BottomTabsNavigator -> Home -> HomeFeed` (fallback)

### Notification Scenarios Handled

1. **App opened from notification (app was closed)**
   - Uses `messaging().getInitialNotification()`
   - Includes 2-second delay to ensure app is fully loaded

2. **App opened from notification (app was in background)**
   - Uses `messaging().onNotificationOpenedApp()`
   - Immediate navigation

3. **Notification received while app is in foreground**
   - Uses `messaging().onMessage()`
   - Shows toast notification that can be tapped to navigate

## Testing

### Manual Testing with Test Utils

You can test the notification redirection functionality using the provided test utilities:

```javascript
// Import in any component (e.g., HomeFeedScreen.tsx)
import { NotificationTests } from '../../utils/NotificationTestUtils';

// Test different scenarios
NotificationTests.challenge('challenge123');  // Test challenge redirection
NotificationTests.user('user456');           // Test user profile redirection
NotificationTests.unknown();                 // Test unknown type (goes to home)
NotificationTests.empty();                   // Test empty notification (goes to home)
NotificationTests.all();                     // Test all scenarios with delays
```

### Testing with Real Notifications

To test with real Firebase notifications, send notifications with the following payload structure:

```json
{
  "to": "DEVICE_FCM_TOKEN",
  "data": {
    "type": "challenge",
    "id": "abc123",
    "title": "New Challenge!",
    "body": "Check out this challenge."
  },
  "notification": {
    "title": "New Challenge!",
    "body": "Check out this challenge."
  }
}
```

## Usage Examples

### Example 1: Challenge Notification
```javascript
// Notification payload
{
  data: {
    type: "challenge",
    id: "challenge_123",
    title: "New Challenge Available!",
    body: "A new fitness challenge has been created."
  }
}
// Result: Navigates to ChallengeDetails screen with challengeId="challenge_123"
```

### Example 2: User Profile Notification
```javascript
// Notification payload
{
  data: {
    type: "user",
    id: "user_456",
    title: "New Follower!",
    body: "Someone started following you."
  }
}
// Result: Navigates to OtherUserProfileScreen with userId="user_456"
```

### Example 3: Fallback Notification
```javascript
// Notification payload
{
  data: {
    type: "unknown_type",
    id: "some_id",
    title: "General Notification",
    body: "Something happened in the app."
  }
}
// Result: Navigates to HomeFeed screen (fallback)
```

## Error Handling

- If `type` or `id` is missing, the app redirects to the home feed
- If an unknown `type` is provided, the app redirects to the home feed
- All navigation attempts are logged to the console for debugging
- Navigation only occurs if the navigation reference is ready

## Future Enhancements

Potential improvements that could be added:

1. Support for more notification types (posts, comments, etc.)
2. Deep linking integration for external app launches
3. Notification history/persistence
4. Custom notification actions
5. Rich notification content support
