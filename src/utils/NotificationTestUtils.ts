import { handleNotificationRedirect, NotificationData } from './NotificationService';

/**
 * Test utility functions for notification redirection
 * These functions can be used to test notification routing without actual Firebase notifications
 */

/**
 * Test challenge notification redirection
 * @param challengeId - The challenge ID to test with
 */
export const testChallengeNotification = (challengeId: string = 'abc123') => {
  console.log('Testing challenge notification redirection...');
  const notificationData: NotificationData = {
    type: 'challenge',
    id: challengeId,
    title: 'New Challenge!',
    body: 'Check out this challenge.',
  };
  
  handleNotificationRedirect(notificationData);
};

/**
 * Test user profile notification redirection
 * @param userId - The user ID to test with
 */
export const testUserNotification = (userId: string = 'user123') => {
  console.log('Testing user notification redirection...');
  const notificationData: NotificationData = {
    type: 'user',
    id: userId,
    title: 'New User Activity!',
    body: 'Check out this user profile.',
  };
  
  handleNotificationRedirect(notificationData);
};

/**
 * Test unknown notification type (should redirect to home feed)
 */
export const testUnknownNotification = () => {
  console.log('Testing unknown notification redirection...');
  const notificationData: NotificationData = {
    type: 'unknown',
    id: 'some-id',
    title: 'Unknown Notification',
    body: 'This should redirect to home feed.',
  };
  
  handleNotificationRedirect(notificationData);
};

/**
 * Test notification without type or id (should redirect to home feed)
 */
export const testEmptyNotification = () => {
  console.log('Testing empty notification redirection...');
  const notificationData: NotificationData = {
    title: 'Empty Notification',
    body: 'This should redirect to home feed.',
  };
  
  handleNotificationRedirect(notificationData);
};

/**
 * Test all notification scenarios
 */
export const testAllNotificationScenarios = () => {
  console.log('Running all notification tests...');
  
  setTimeout(() => testChallengeNotification(), 1000);
  setTimeout(() => testUserNotification(), 2000);
  setTimeout(() => testUnknownNotification(), 3000);
  setTimeout(() => testEmptyNotification(), 4000);
};

// Export test functions for easy access in development
export const NotificationTests = {
  challenge: testChallengeNotification,
  user: testUserNotification,
  unknown: testUnknownNotification,
  empty: testEmptyNotification,
  all: testAllNotificationScenarios,
};
