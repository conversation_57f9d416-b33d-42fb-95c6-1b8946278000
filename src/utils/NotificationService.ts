import { navigate } from './NavigationService';

export interface NotificationData {
  type?: string;
  id?: string;
  title?: string;
  body?: string;
}

export interface NotificationPayload {
  data?: NotificationData;
  notification?: {
    title?: string;
    body?: string;
  };
}

/**
 * Handles notification redirection based on notification data
 * @param notificationData - The notification data containing type and id
 */
export const handleNotificationRedirect = (notificationData: NotificationData) => {
  console.log('Handling notification redirect:', notificationData);

  const { type, id } = notificationData;

  // If type and id are not known, redirect to home feed screen
  if (!type || !id) {
    console.log('No type or id found, redirecting to home feed');
    redirectToHomeFeed();
    return;
  }

  // Handle different notification types
  switch (type.toLowerCase()) {
    case 'challenge':
      redirectToChallenge(id);
      break;
    case 'user':
    case 'profile':
      redirectToUserProfile(id);
      break;
    default:
      console.log(`Unknown notification type: ${type}, redirecting to home feed`);
      redirectToHomeFeed();
      break;
  }
};

/**
 * Redirects to challenge details screen
 * @param challengeId - The challenge ID
 */
const redirectToChallenge = (challengeId: string) => {
  console.log(`Redirecting to challenge: ${challengeId}`);

  // Navigate to BottomTabsNavigator -> Home tab -> ChallengeDetails screen
  navigate('BottomTabsNavigator', {
    screen: 'Home',
    params: {
      screen: 'ChallengeDetails',
      params: {
        challengeId: challengeId,
      },
    },
  });
};

/**
 * Redirects to user profile screen
 * @param userId - The user ID
 */
const redirectToUserProfile = (userId: string) => {
  console.log(`Redirecting to user profile: ${userId}`);

  // Navigate to BottomTabsNavigator -> Home tab -> OtherUserProfileScreen
  navigate('BottomTabsNavigator', {
    screen: 'Home',
    params: {
      screen: 'OtherUserProfileScreen',
      params: {
        userId: userId,
      },
    },
  });
};

/**
 * Redirects to home feed screen as fallback
 */
const redirectToHomeFeed = () => {
  console.log('Redirecting to home feed');
  
  // Navigate to BottomTabsNavigator -> Home tab -> HomeFeed screen
  navigate('BottomTabsNavigator', {
    screen: 'Home',
    params: {
      screen: 'HomeFeed',
    },
  });
};

/**
 * Processes notification payload and extracts relevant data
 * @param payload - The complete notification payload
 * @returns Processed notification data
 */
export const processNotificationPayload = (payload: NotificationPayload): NotificationData => {
  const data = payload.data || {};
  const notification = payload.notification || {};

  return {
    type: data.type,
    id: data.id,
    title: data.title || notification.title,
    body: data.body || notification.body,
  };
};
