import {createAsyncThunk} from '@reduxjs/toolkit';
import challengeService from './ChallengeService';

export const getChallenge: any = createAsyncThunk(
  'challenge/getChallenge',
  async (challengeId, thunkAPI) => {
    try {
      console.log(
        'Get Challenge action service was run for challengeId:',

        challengeId,
      );
      const response: any = await challengeService.getChallenge(challengeId);
      return response;
    } catch (error) {
      console.log('Get Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getChallengeForUpdate: any = createAsyncThunk(
  'challenge/getChallengeForUpdate',
  async (challengeId, thunkAPI) => {
    try {
      const response: any = await challengeService.getChallenge(challengeId);
      return response;
    } catch (error) {
      console.log('Get Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const createNewChallenge: any = createAsyncThunk(
  'challenge/createNewChallenge',
  async (challengeData, thunkAPI) => {
    try {
      console.log(
        'Create Challenge action service was run',
        'challengedata:',
        challengeData,
      );
      const response = await challengeService.createNewChallenge(challengeData);
      return response;
    } catch (error) {
      console.log('Create Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const updateChallenge: any = createAsyncThunk(
  'challenge/updateChallenge',
  async (Updatedata, thunkAPI) => {
    // const [challengeId] = Updatedata;
    try {
      console.log('Update challenge action service was run');
      console.log('Updated challenge data', Updatedata);
      const response = await challengeService.updateChallenge(Updatedata);
      return response;
    } catch (error) {
      console.log('Update Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const getChallengeFeed: any = createAsyncThunk(
  'challenge/getChallengeFeed',
  async (
    {userId, nextPageToken}: {userId: string; nextPageToken: string},
    thunkAPI,
  ) => {
    try {
      console.log(
        'Get Challenge Feed action service was run',
        'userdata:',
        userId,
      );
      const response: any = await challengeService.getChallengeFeed(
        userId,
        nextPageToken,
      );
      return response;
    } catch (error) {
      console.log('Get Challenge feed action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const getChallengeCompletedFeed: any = createAsyncThunk(
  'challenge/getChallengeCompletedFeed',
  async (
    {userId, nextPageToken}: {userId: string; nextPageToken: string},
    thunkAPI,
  ) => {
    try {
      console.log(
        'Get Challenge Feed action service was run',
        'userdata:',
        userId,
      );
      const response: any = await challengeService.getChallengeCompletedFeed(
        userId,
        nextPageToken,
      );
      return response;
    } catch (error) {
      console.log('Get Challenge feed action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const getChallengeCreatedByUser: any = createAsyncThunk(
  'challenge/getChallengeCreatedByUser',
  async (
    {
      userId,
      nextPageToken,
      leaderboardCreated,
    }: {userId: string; nextPageToken: string; leaderboardCreated?: boolean},
    thunkAPI,
  ) => {
    try {
      console.log(
        'Get Challenge Feed action service was run',
        'userdata:',
        userId,
      );
      const response: any = await challengeService.getChallengeCreatedByUser(
        userId,
        nextPageToken,
        leaderboardCreated,
      );
      return response;
    } catch (error) {
      console.log('Get Challenge feed action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getChallengeFeedExplore: any = createAsyncThunk(
  'challenge/getChallengeFeedExplore',
  async ({nextPageToken, isActive}: {nextPageToken?: string; isActive?: boolean}, thunkAPI) => {
    try {
      const response: any = await challengeService.getChallengeExporeFeed(
        nextPageToken,
        isActive
      );
      return response;
    } catch (error) {
      console.log('Get Challenge explore feed action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const deleteChallenge: any = createAsyncThunk(
  'challenge/deleteChallenge',
  async (challengeId, thunkAPI) => {
    try {
      const response: any = await challengeService.deleteChallenge(challengeId);
      console.log('Delete Challenge action service was run',response);
      return response;
    } catch (error) {
      console.log('Delete Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const addChallengeMedia: any = createAsyncThunk(
  'posts/addChallengeMedia',
  async ({challengeId, mediaFile}: any, thunkAPI) => {
    try {
      console.log(
        'Add Post Media action service was run',
        'challengeId:',
        challengeId,
        'mediaFile',
        mediaFile,
      );
      const response = await challengeService.addChallengeMedia(
        challengeId,
        mediaFile,
      );
      return response;
    } catch (error: any) {
      console.log(
        'Add Post Media action service error:',
        error.response?.data || error.message,
      );
      return thunkAPI.rejectWithValue(error.response?.data || error.message);
    }
  },
);

export const joinChallenge: any = createAsyncThunk(
  'challenge/joinChallenge',
  async (challengeData: {userId: string; challengeId: string}, thunkAPI) => {
    try {
      const response = await challengeService.joinChallenge(challengeData);
      return response;
    } catch (error) {
      console.log('Create Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const exitChallenge: any = createAsyncThunk(
  'challenge/exitChallenge',
  async (challengeData: {userId: string; challengeId: string}, thunkAPI) => {
    try {
      const response = await challengeService.exitChallenge(challengeData);
      return response;
    } catch (error) {
      console.log('Create Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const searchChallenge: any = createAsyncThunk(
  'challenge/searchChallenge',
  async (body: {challengeTitle: string}, thunkAPI) => {
    try {
      const response: any = await challengeService.searchChallenge(body);
      return response;
    } catch (error) {
      console.log('searchChallenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const deleteChallengeMedia: any = createAsyncThunk(
  'challenge/deleteChallengeMedia',
  async (updateData, thunkAPI) => {
    try {
      console.log(
        'delete challenge action service was run',
        'postdata:',
        updateData,
      );
      const response = await challengeService.deleteChallengeMedia(updateData);
      return response;
    } catch (error) {
      console.log('delete challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const getParticipantsOfChallenge: any = createAsyncThunk(
  'challenge/getParticipantsOfChallenge',
  async (challengeId: string, thunkAPI) => {
    try {
      const response = await challengeService.getParticipantsOfChallenge(
        challengeId,
      );
      return response;
    } catch (error) {
      console.log('getParticpants action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const getPostLinkedToChallenge: any = createAsyncThunk(
  'challenge/getPostLinkedToChallenge',
  async (data: {challengeId: string; nextPageToken?: string}, thunkAPI) => {
    try {
      const response = await challengeService.getPostLinkedToChallenge(data);
      return response;
    } catch (error) {
      console.log('Post linked to challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const generateDetailsFromAI: any = createAsyncThunk(
  'challenge/generateDetailsFromAI',
  async (data: {prompt: string; isPost: boolean}, thunkAPI) => {
    try {
      const response = await challengeService.generateDetailsFromAI(data);
      return response;
    } catch (error) {
      console.log('AI generation action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const reGenerateDetailsFromAI: any = createAsyncThunk(
  'challenge/reGenerateDetailsFromAI',
  async (data: {type: string; sessionId: string; isPost: boolean}, thunkAPI) => {
    try {
      const response = await challengeService.reGenerateDetailsFromAI(data);
      return response;
    } catch (error) {
      console.log('AI regeneration action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const deleteSessionFromAI: any = createAsyncThunk(
  'challenge/deleteSessionFromAI',
  async (_, thunkAPI) => {
    try {
      const response = await challengeService.deleteSessionFromAI();
      return response;
    } catch (error) {
      console.log('delete AI session action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getLikeUserList: any = createAsyncThunk(
  'posts/getLikeUserList',
  async (data: any, thunkAPI) => {
    try {
      const response = await challengeService.getLikeUserList(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);
export const finalizeChallenge: any = createAsyncThunk(
  'challenge/finalizeChallenge',
  async (challengeId, thunkAPI) => {
    // const [challengeId] = Updatedata;
    try {
      console.log('Finalize challenge action service was run');
      console.log('Finalize challenge Id', challengeId);
      console.log('tyoeOf', typeof(challengeId));
      const response = await challengeService.finalizeChallenge(challengeId);
      return response;
    } catch (error) {
      console.log('Finalize Challenge action service was not run');
      return thunkAPI.rejectWithValue(error);
    }
  },
);
