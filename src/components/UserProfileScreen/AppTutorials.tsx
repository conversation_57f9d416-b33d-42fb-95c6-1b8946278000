import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Button,
  TouchableHighlight,
  Alert,
  Linking,
  Platform
} from 'react-native';
import acrossAllScreens from '../../styles/acrossAllScreens';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SignUpStyle from '../../styles/SignUp';
import {useDispatch, useSelector} from 'react-redux';
import {deleteUser, logout} from '../../redux/User/UserAction';
import {resetDeleteSuccess} from '../../redux/User/UserSlice';

export function AppTutorialsScreen({route, navigation}: any) {
  let userData: any;
  let title: string;
  let message: string;
  const userState = useSelector(state => state.user);
  const {isDeleteSuccess} = userState;
  const authState = useSelector(state => state.auth);
  const {userId} = authState;
  const [arrow, setArrow] = useState(true);
  const toggleArrow = () => {
    console.log('Arrow button press');
    setArrow(prevState => !prevState);
  };
  const dispatch = useDispatch();

  return (
    <View style={styles.container}>
      <View style={[styles.HeaderStyle]}>
        <TouchableOpacity
          style={[acrossAllScreens.backImageContainer, styles.HeaderbackButton]}
          onPress={() => navigation.goBack()}>
          <Image
            style={acrossAllScreens.backImage}
            source={require('../../assets/images/back.png')}
          />
        </TouchableOpacity>
        <View style={styles.HeaderHorizontalPosition}>
          <Text style={[acrossAllScreens.ScreenHeaderText]}>App Tutorials</Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
            navigation.push('TutorialScreen1');
          }}
        >
        <Text style={acrossAllScreens.H2}>App Intro</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
            Linking.openURL('https://youtube.com/shorts/q7IzSi7c-VI?feature=share');
          }}>
        <Text style={acrossAllScreens.H2}>How to use the app video</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      {Platform.OS === 'android' ? (
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
            Linking.openURL('https://youtube.com/shorts/FwS_gznWSxE?feature=share');
          }}>
        <Text style={acrossAllScreens.H2}>How to connect health data on android</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      ): null}
            {Platform.OS === 'ios' ? (
      <TouchableOpacity
        style={[styles.Button, styles.ButtonGap]}
        onPress={() => {
            Linking.openURL('https://youtube.com/shorts/d0mTTTcKv6o?si=Qw3et0OTgwLC2rmg');
          }}>
        <Text style={acrossAllScreens.H2}>How to connect health data on iOS</Text>
        <AntDesign name="right" size={20} color="black" />
      </TouchableOpacity>
      ): null}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 25,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editProfileText: {
    marginTop: 10,
    // color: 'black',
    // fontWeight:
  },
  inputField: {
    borderWidth: 1,
    borderColor: 'black',
    padding: 10,
    marginVertical: 10,
    borderRadius: 8,
    color: 'black',
  },
  inputLine: {
    borderBottomWidth: 0.2,
    borderColor: 'black',
    paddingBottom: 0.5,
    paddingTop: 2,
    marginBottom: 10,
    color: 'black',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  HeaderStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },

  HeaderbackButton: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  HeaderHorizontalPosition: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ButtonPos: {
    marginTop: 70,
    alignItems: 'center',
  },
  Button: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ButtonGap: {marginBottom: 40},
});

export default AppTutorialsScreen;
