import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
  AppState,
  PermissionsAndroid,
  Animated,
} from 'react-native';
import {FlashList} from '@shopify/flash-list';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Tabs} from 'react-native-collapsible-tab-view';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {
  deletePost,
  deletePublicFeedChallenge,
  fetchFollowedPosts,
  fetchPublicPosts,
  likeUnlikeChallengeFromPost,
  likeUnlikePost,
  removeLikeUnlikeChallengeFromPost,
  removeLikeUnlikePost,
} from '../../redux/Post/PostAction';
import ChallengeRecommendations from './ChallengeRecommendations';
import {useAppDispatch, useAppSelector} from '../../redux/Store';
import {baseUrl, isVideoLink} from '../../utils/Utils';
import Video from 'react-native-video';
import {
  ViewPortDetector,
  ViewPortDetectorProvider,
} from 'react-native-viewport-detector';
import FastImage from 'react-native-fast-image';
const {width} = Dimensions.get('window');
import moment from 'moment-timezone';
import Popover from 'react-native-popover-view';
import {useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {deleteChallenge} from '../../redux/Challenge/ChallengeAction';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/Ionicons';
import messaging from '@react-native-firebase/messaging';
import analytics from '@react-native-firebase/analytics';
// Uncomment the line below to test notification redirection functionality
// import { NotificationTests } from '../../utils/NotificationTestUtils';
import {muteUnMuteVideo} from '../../redux/Post/PostSlice';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import convertToProxyURL from 'react-native-video-cache';
import DebounceTouchable from './DebounceTouchable';

const screenWidth = Dimensions.get('window').width;

export const Dots = ({length, activeIndex}: any) => (
  <View style={styles.dotsContainer}>
    {new Array(length).fill(0).map((_, idx) => (
      <View
        key={idx}
        style={[
          styles.dot,
          idx === activeIndex ? styles.activeDot : styles.inactiveDot,
        ]}
      />
    ))}
  </View>
);
// Component for rendering each post
const PostCard = ({
  post,
  index,
  navigation,
  tabIndex,
  tab,
  visibleIndex,
  appState,
}: any) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const user = useSelector((state: any) => state.auth.user);
  const [inViewPort, setInViewPort] = useState(false);
  const popoverRef = useRef<Popover>(null);
  const isOwnPost = post.user?.userId == user.userId;
  const userState = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const {token} = userState;
  const currentUserId = useAppSelector(state => state.auth.userId);
  const mute = useAppSelector(state => state.post.mute);
  const [isMuted, setIsMuted] = useState(mute);

  useEffect(() => {
    setIsMuted(mute);
  }, [mute]);

  const toggleMute = () => {
    dispatch(muteUnMuteVideo(!isMuted));
  };

  const handleScroll = (event: any) => {
    const position = event.nativeEvent.contentOffset.x;
    const activeIndex = Math.round(position / (screenWidth - 32));
    setActiveIndex(activeIndex);
  };

  const onEditClick = () => {
    popoverRef.current?.requestClose();
    if (post.isLeaderboardCreated == undefined) {
      navigation.navigate('CreatePostScreenStack', {
        screen: 'UpdatePostScreen',
        params: {postId: post.id},
      });
    } else {
      navigation.push('ChallengeUpdate', {
        title: post?.title,
        challengeId: post?.id,
        description: post?.description,
        startDate: post?.startDate,
        endDate: post?.endDate,
        isLeaderboardCreated: post?.isLeaderboardCreated,
        pointsTitle: post?.pointsTitle,
        pointsDescription: post?.pointsDescription,
        isPointsAutomated: post?.isPointsAutomated,
        isPointsAscending: post?.isPointsAscending,
        difficultyRating: post?.difficultyRating,
        visibility: post?.visibility,
        mediaRefs: post?.mediaRefs,
        loggedInUser: user.userId,
        tags: post?.tags,
      });
    }
  };

  const onDeleteClick = () => {
    popoverRef.current?.requestClose();
    const isPost = post.isLeaderboardCreated == undefined;
    Alert.alert(
      isPost ? 'Delete Post' : 'Delete Challenge',
      isPost
        ? 'Are you sure you want to delete this post?'
        : 'Are you sure you want to delete this challenge?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            (isPost?console.log('post',post?.id):
            console.log('Challenge',post?.challengeId,'real challenge id',post?.id))

            const result = await dispatch(
              isPost
                ? deletePost(post?.id)
                : deletePublicFeedChallenge(post?.id),
                // : deleteChallenge(post?.id),
            );
            if (result.payload.code == 200) {
            } else {
              Toast.show({
                type: 'error',
                text1: 'Failed to delete content.',
                text2: 'Something went wrong.',
              });
            }
          },
          style: 'destructive',
        },
      ],
    );
  };

  const onReportClick = () => {
    popoverRef.current?.requestClose();
  };

  const onChallnegePress = (item: any) => {
    if (item.startDate) {
      navigation.navigate('ChallengeDetails', {challengeId: item.id});
    } else if (item.challengeId) {
      navigation.navigate('ChallengeDetails', {challengeId: item.challengeId});
    }
  };

  const onUserClick = () => {
    if (currentUserId == post.user?.userId) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('OtherUserProfileScreen', {
        userId: post.user?.userId,
      });
    }
  };

  const onLikeClick = () => {
    if (post.isLeaderboardCreated == undefined) {
      if (post.isLiked) {
        dispatch(
          removeLikeUnlikePost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: post.id,
          }),
        );
      } else {
        dispatch(
          likeUnlikePost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: post.id,
          }),
        );
      }
    } else {
      if (post.isLiked) {
        dispatch(
          removeLikeUnlikeChallengeFromPost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: post.id,
          }),
        );
      } else {
        dispatch(
          likeUnlikeChallengeFromPost({
            actionType: 'LIKES',
            interaction: 'POSITIVE',
            contentId: post.id,
          }),
        );
      }
    }
  };

  const onLikeCountPress = () => {
    if (post.isLeaderboardCreated == undefined) {
      navigation.navigate('PostLikeUser', {postId: post.id});
    } else {
      navigation.navigate('ChallengeLikeUser', {challengeId: post.id});
    }
  };

  return (
    <ViewPortDetector
      onChange={setInViewPort}
      percentHeight={Platform.OS == 'ios' ? 0.9 : 1}
      percentWidth={1}
      frequency={300}>
      <View style={styles.postContainer}>
        {/* Post Header (User profile picture, name, and time) */}
        <TouchableOpacity
          onPress={() => onUserClick()}
          style={styles.postHeader}>
          {/* User Profile Picture */}
          <View style={styles.profilePicContainer}>
            <FastImage
              source={
                post.user?.imageReference &&
                post.user?.imageReference !== 'imageReference'
                  ? {
                      uri: post.user?.imageReference,
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  : require('../../static/Images/user.png')
              }
              style={styles.profilePic}
            />
          </View>
          {/* User Info (Name and Time Ago) */}
          <View style={styles.userInfoContainer}>
            <Text
              style={
                styles.userName
              }>{`${post.user?.firstName} ${post.user?.lastName}`}</Text>
            <Text style={styles.timeAgo}>
              {moment.utc(post.updatedAt).fromNow()}
            </Text>
          </View>
          {/* Options Icon */}
          <Popover
            ref={popoverRef}
            from={
              <TouchableOpacity>
                <Image
                  source={require('../../assets/images/more.png')}
                  resizeMode="contain"
                  style={styles.moreIcon}
                />
              </TouchableOpacity>
            }
            popoverStyle={styles.popoverStyle}
            backgroundStyle={{opacity: 0}}>
            {isOwnPost ? (
              <>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onEditClick}>
                  <Text>Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={onDeleteClick}>
                  <Text>Delete</Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity
                style={styles.optionItem}
                onPress={onReportClick}>
                <Text>Report</Text>
              </TouchableOpacity>
            )}
          </Popover>
        </TouchableOpacity>

        {post.isLeaderboardCreated != undefined ? (
          <View style={styles.challengeContainer}>
            {/* <TouchableOpacity
                  onPress={() => onChallnegePress(post)} style={styles.challengeTextContainer}>  */}

            <View style={styles.challengeTextContainer}>
              <Image
                style={styles.challengeIcon}
                source={require('../../assets/images/challengeBig.png')}
              />
              <Text numberOfLines={2} style={styles.challengeTitle}>
                {post.title}
              </Text>
            </View>
            {/* </TouchableOpacity> */}
            <View style={styles.challengeInfoContainer}>
              {post.mediaRefs && (
                <FlatList
                  data={post.mediaRefs}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  pagingEnabled
                  onScroll={handleScroll}
                  nestedScrollEnabled
                  scrollEventThrottle={16}
                  style={{maxWidth: (screenWidth - 80) / 2}}
                  keyExtractor={(img, idx) => idx.toString()}
                  renderItem={({item: imageSrc, index}) => (
                    <View style={styles.challengeImageContainer}>
                      {isVideoLink(imageSrc) ? (
                        <View style={styles.videoWrapper}>
                          <Video
                            source={{
                              uri: convertToProxyURL(imageSrc),
                              headers: {
                                Authorization: `Bearer ${token}`,
                              },
                            }}
                            repeat
                            paused={
                              !(
                                inViewPort &&
                                activeIndex === index &&
                                tab === tabIndex &&
                                visibleIndex
                              )
                            }
                            useTextureView={false}
                            muted={isMuted || appState != 'active'}
                            ignoreSilentSwitch="ignore"
                            resizeMode="cover"
                            style={styles.imageChallenge}
                            bufferConfig={{
                              minBufferMs: 2500,
                              maxBufferMs: 50000,
                              bufferForPlaybackMs: 2500,
                              bufferForPlaybackAfterRebufferMs: 2500,
                            }}
                          />
                          <TouchableOpacity
                            onPress={toggleMute}
                            style={styles.muteButton}>
                            <Icon
                              name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
                              size={20}
                              color="white"
                            />
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <FastImage
                          source={{
                            uri: imageSrc,
                            headers: {
                              Authorization: `Bearer ${token}`,
                            },
                          }}
                          style={styles.imageChallenge}
                          resizeMode="cover"
                        />
                      )}
                    </View>
                  )}
                />
              )}
              <View style={styles.challengeDetailsContainer}>
                <Text style={styles.descriptionText}>
                  {post.text || post.description}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => onChallnegePress(post)}
              style={styles.joinButton}>
              <Text style={styles.btnText}>Dive In</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {post.mediaRefs && (
              <FlatList
                data={post.mediaRefs}
                horizontal
                showsHorizontalScrollIndicator={false}
                pagingEnabled
                onScroll={handleScroll}
                nestedScrollEnabled
                scrollEventThrottle={16}
                keyExtractor={(img, idx) => idx.toString()}
                renderItem={({item: imageSrc, index}) => (
                  <View style={styles.imageContainer}>
                    {isVideoLink(imageSrc) ? (
                      <View style={styles.videoWrapper}>
                        <Video
                          source={{
                            uri: convertToProxyURL(imageSrc),
                            headers: {
                              Authorization: `Bearer ${token}`,
                            },
                          }}
                          repeat
                          paused={
                            !(
                              inViewPort &&
                              activeIndex === index &&
                              tab === tabIndex &&
                              visibleIndex
                            )
                          }
                          useTextureView={false}
                          muted={isMuted || appState != 'active'}
                          ignoreSilentSwitch="ignore"
                          resizeMode="contain"
                          style={styles.image}
                          bufferConfig={{
                            minBufferMs: 2500,
                            maxBufferMs: 50000,
                            bufferForPlaybackMs: 2500,
                            bufferForPlaybackAfterRebufferMs: 2500,
                          }}
                        />
                        <TouchableOpacity
                          onPress={toggleMute}
                          style={styles.muteButton}>
                          <Icon
                            name={isMuted ? 'volume-mute' : 'volume-high'} // Use icons for mute/unmute
                            size={20}
                            color="white"
                          />
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <FastImage
                        source={{
                          uri: imageSrc,
                          headers: {
                            Authorization: `Bearer ${token}`,
                          },
                        }}
                        style={styles.image}
                        resizeMode="contain"
                      />
                    )}
                  </View>
                )}
              />
            )}
            {post.mediaRefs && post.mediaRefs.length > 1 && (
              <Dots length={post.mediaRefs.length} activeIndex={activeIndex} />
            )}
          </>
        )}

        {/* Post Title and Like Button (Same Row) */}
        <View style={styles.titleAndLikeRow}>
          {post.isLeaderboardCreated != undefined ? (
            //  <Text style={styles.challengeInfoTxt}>Be the first challenger</Text>
            <View />
          ) : (
            <View style={styles.LikeRowText}>
           {post.title ? (<TouchableOpacity
              style={{marginTop: 5}}
              onPress={() => onChallnegePress(post)}>
              {post.title && <Text style={styles.postTitle}>{post.title}</Text>}
            </TouchableOpacity> ):  (<Text style={styles.postBody}>{post.text || post.description}</Text>)}
            </View>
          )}

          <DebounceTouchable
            delay={5000}
            style={styles.likeButton}
            onPress={onLikeClick}>
            <Image
              style={styles.likeImage}
              source={
                post.isLiked
                  ? require('../../assets/images/like.png')
                  : require('../../assets/images/unlike.png')
              }
            />
            {isOwnPost && (
              <TouchableOpacity onPress={onLikeCountPress}>
                <Text style={styles.likeCount}>{`${post.likes}`} </Text>
              </TouchableOpacity>
            )}
          </DebounceTouchable>
        </View>

        {/* Post Body */}
        {post.isLeaderboardCreated == undefined && post.title && (
          <Text style={styles.postBody}>{post.text || post.description}</Text>
        )}
      </View>
    </ViewPortDetector>
  );
};

let bottomLoad = false;

export function HomeFeedScreen({navigation}: any) {
  const authState = useAppSelector(state => state.auth);
  const publicPost = useAppSelector(state => state.post.publicPost.data);
  const nextPageToken = useAppSelector(
    state => state.post.publicPost.nextPageToken,
  );
  const hasNext = useAppSelector(state => state.post.publicPost.hasNext);
  const followPost = useAppSelector(state => state.post.followPost.data);
  const followNextPageToken = useAppSelector(
    state => state.post.followPost.nextPageToken,
  );
  const followHasNext = useAppSelector(state => state.post.followPost.hasNext);
  const dispatch = useAppDispatch();
  const {userId} = authState;
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const tabRef = useRef<any>(null);
  const [visibleIndexes, setVisibleIndexes] = useState<Array<number>>([]);
  const [visibleIndexesFollowed, setVisibleIndexesFollowed] = useState<
    Array<number>
  >([]);
  const [appState, setAppState] = useState('active');
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isLogoShow, setLogoShow] = useState(true);
  const SCROLL_OFFSET_THRESHOLD = 20;
  const SCROLL_DELAY = 300; // 500ms delay
  const timeoutRef = useRef<any>(null);
  const publicListRef = useRef<FlashList<any>>(null);
  const friendListRef = useRef<FlashList<any>>(null);

  const onLogoPress = () => {
    publicListRef.current?.scrollToOffset({animated: true, offset: 0});
    friendListRef.current?.scrollToOffset({animated: true, offset: 0});
  };

  const handleScroll = (event: any) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (currentScrollY === 0) {
        setLogoShow(true);
      } else if (currentScrollY > lastScrollY + SCROLL_OFFSET_THRESHOLD) {
        setLogoShow(false); // Scrolling Down
      } else if (currentScrollY < lastScrollY - SCROLL_OFFSET_THRESHOLD) {
        setLogoShow(true); // Scrolling Up
      }

      setLastScrollY(currentScrollY);
    }, SCROLL_DELAY);
  };

  async function requestUserNotificationPermission() {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('Authorization status (iOS):', authStatus);
      }
    } else if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );

      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('Notification permission granted (Android)');
      } else {
        console.log('Notification permission denied (Android)');
      }
    }
  }
  useEffect(() => {
    analytics().logScreenView({
      screen_name: 'HomeFeedScreen',
      screen_class: 'HomeFeedScreen',
    });
    setTimeout(() => {
      requestUserNotificationPermission();
    }, 1000);
    const handleAppStateChange = (nextAppState: string) => {
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const onViewableItemsChanged = ({viewableItems, changed}: any) => {
    const currentVisibleIndexes = viewableItems.map((item: any) => item.index);
    setVisibleIndexes(currentVisibleIndexes);
  };

  const onViewableItemsChangedFollowed = ({viewableItems, changed}: any) => {
    const currentVisibleIndexes = viewableItems.map((item: any) => item.index);
    setVisibleIndexesFollowed(currentVisibleIndexes);
  };

  useFocusEffect(
    useCallback(() => {
      if (tabRef.current) {
        setTabIndex(tabRef.current?.getCurrentIndex());
      }
      return () => {
        setTabIndex(-1);
      };
    }, []),
  );

  useEffect(() => {
    dispatch(fetchPublicPosts({}));
    dispatch(fetchFollowedPosts({}));
  }, []);

  const onGlobalPostEnd = useCallback(() => {
    if (publicPost.length > 0 && !loading && !bottomLoad && hasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(fetchPublicPosts({nextPageToken: nextPageToken})).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, publicPost, nextPageToken, loading, hasNext]);

  const onFollowPostEnd = useCallback(() => {
    if (followPost.length > 0 && !loading && !bottomLoad && followHasNext) {
      bottomLoad = true;
      setLoading(true);
      dispatch(
        fetchFollowedPosts({nextPageToken: followNextPageToken}),
      ).finally(() => {
        setLoading(false);
        bottomLoad = false;
      });
    }
  }, [dispatch, followPost, followNextPageToken, loading, followHasNext]);

  const renderFooter = () => {
    return loading ? (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#87CEEB" />
      </View>
    ) : null;
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(fetchPublicPosts({})).finally(() => setRefreshing(false));
  }, [dispatch]);

  const onFollowRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(fetchFollowedPosts({})).finally(() => setRefreshing(false));
  }, [dispatch]);

  // Content for the Friends tab
  const renderFriendsPostContent = (
    <FlashList
      data={followPost}
      ref={friendListRef}
      renderItem={({item, index}) => (
        <PostCard
          post={item}
          index={index}
          appState={appState}
          navigation={navigation}
          tabIndex={tabIndex}
          tab={0}
          visibleIndex={visibleIndexesFollowed.includes(index)}
        />
      )}
      extraData={tabIndex + visibleIndexesFollowed.join(',') + appState}
      onEndReached={onFollowPostEnd}
      onEndReachedThreshold={0.1}
      estimatedItemSize={535}
      onScroll={handleScroll}
      onMomentumScrollBegin={handleScroll}
      ListFooterComponent={renderFooter}
      scrollEventThrottle={16}
      onViewableItemsChanged={onViewableItemsChangedFollowed}
      viewabilityConfig={{
        itemVisiblePercentThreshold: 50, // Adjust as needed
      }}
      refreshing={refreshing}
      onRefresh={onFollowRefresh}
      keyExtractor={(item, index) => index.toString()}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={() => <ChallengeRecommendations navigation={navigation} />}
      contentContainerStyle={{paddingTop: 40}} // Padding to avoid the first post being covered by tabs
    />
  );

  // Content for the Global tab
  const renderGlobalPostContent = (
    <FlashList
      data={publicPost}
      ref={publicListRef}
      keyExtractor={(item, index) => index.toString()}
      onEndReachedThreshold={0.1}
      estimatedItemSize={535}
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={{
        itemVisiblePercentThreshold: 50, // Adjust as needed
      }}
      extraData={tabIndex + visibleIndexes.join(',') + appState}
      scrollEventThrottle={16}
      onEndReached={onGlobalPostEnd}
      ListFooterComponent={renderFooter}
      onScroll={handleScroll}
      onMomentumScrollBegin={handleScroll}
      refreshing={refreshing}
      onRefresh={onRefresh}
      renderItem={({item, index}) => (
        <PostCard
          post={item}
          index={index}
          navigation={navigation}
          appState={appState}
          tabIndex={tabIndex}
          tab={1}
          visibleIndex={visibleIndexes.includes(index)}
        />
      )}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={() => <ChallengeRecommendations navigation={navigation} />}
      contentContainerStyle={{paddingTop: 40}} // Padding to avoid the first post being covered by tabs
    />
  );

  return (
    <ViewPortDetectorProvider flex={1}>
      <View style={[acrossAllScreens.ScreenBackground, {overflow: 'hidden'}]}>
        {/* Napoz Header */}
        {isLogoShow && (
          <TouchableOpacity
            onPress={onLogoPress}
            style={styles.headerContainer}>
            <Image
              style={acrossAllScreens.logoImage}
              source={require('../../assets/images/napoz.png')}
            />
          </TouchableOpacity>
        )}

        {/* Collapsible Tab View with Icons */}
        <Tabs.Container
          // @ts-ignore
          renderHeader={null}
          ref={tabRef}
          initialTabName={'GlobalTab'}
          tabBarIndicatorStyle={styles.tabBarIndicator}
          pagerProps={{
            scrollEnabled: true,
          }}
          onIndexChange={(index: number) => {
            setTabIndex(index);
          }}
          tabBarStyle={styles.tabBarStyle}>
          <Tabs.Tab
            name="FriendsTab"
            label={() => (
              <Ionicons name="people-sharp" size={28} color="black" />
            )}>
            <View
              style={{
                flexGrow: 1,
                flexDirection: 'row',
              }}>
              {renderFriendsPostContent}
            </View>
          </Tabs.Tab>
          <Tabs.Tab
            name="GlobalTab"
            label={() => <FontAwesome name="globe" size={28} color="black" />}>
            <View
              style={{
                flexGrow: 1,
                flexDirection: 'row',
              }}>
              {renderGlobalPostContent}
            </View>
          </Tabs.Tab>
        </Tabs.Container>
      </View>
    </ViewPortDetectorProvider>
  );
}

const styles = StyleSheet.create({
  likeCount: {
    fontSize: 17,
    color: '#000000',
    fontWeight: '400',
    fontFamily: 'Helvetica Neue',
    marginLeft: 2,
  },
  challengeInfoTxt: {
    fontSize: 14,
    color: '#00000080',
  },
  descriptionText: {
    flex: 1,
    fontSize: 14, //Body
    fontWeight: Platform.select({
      ios: '300', // ios font weights - [100, 200, 300, 400, 500, 600, 700, 800, 900, bold, normal]
      android: '300',
    }),
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  btnText: {
    fontSize: 18,
    fontWeight: '500',
  },
  joinButton: {
    backgroundColor: '#C3E7F5',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 16,
    borderRadius: 8,
  },
  challengeDetailsContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingLeft: 16,
    height: screenWidth - 183.3,
  },
  challengeImageContainer: {
    width: (screenWidth - 80) / 2,
    alignItems: 'center',
  },
  challengeTitle: {
    fontWeight: 'bold',
    fontSize: 18,
    marginLeft: 10,
    flex: 1,
  },
  challengeIcon: {
    width: 27,
    height: 39,
  },
  challengeInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // width : (screenWidth - 48)/2
  },
  challengeTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    flex: 1,
  },
  imageChallenge: {
    width: (screenWidth - 80) / 2,
    height: screenWidth - 183.3,
    // resizeMode: 'contain',
    backgroundColor: '#000000',
    borderRadius: 8,
  },
  challengeContainer: {
    backgroundColor: '#FFC13B',
    padding: 16,
    width: screenWidth - 32,
    height: screenWidth - 32,
    borderRadius: 10,
  },
  videoWrapper: {
    position: 'relative',
  },
  muteButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 5,
  },
  muteIcon: {
    width: 20,
    height: 20,
    tintColor: 'white',
  },
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    width: Dimensions.get('window').width - 70,
  },
  moreIcon: {
    width: 18,
    height: 18,
    marginLeft: 60,
    marginTop: 10,
    marginBottom: 10,
  },
  optionItem: {
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth:40,
  },
  popoverStyle: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
    paddingVertical: 5,
  },
  likeImage: {
    width: 28,
    height: 28,
    top: 2,
  },
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
  activeDot: {
    backgroundColor: 'black',
  },
  inactiveDot: {
    backgroundColor: 'gray',
  },
  image: {
    width: screenWidth - 32,
    height: screenWidth - 32,
    resizeMode: 'cover',
    backgroundColor: '#000000',
  },
  imageContainer: {
    width: screenWidth - 32,
    alignItems: 'center',
  },
  headerContainer: {
    alignItems: 'flex-start', // Align header to the start of the view
    padding: 10, // Adjust padding as needed,
  },
  headerTextContainer: {
    backgroundColor: '#C3E7F5', // Background color for the header
  },
  headerText: {
    fontSize: 50,
    lineHeight: 48,
    color: 'black',
    fontWeight: '600',
    fontFamily: 'Helvetica Neue',
  },
  postContainer: {
    paddingVertical: 10,
    backgroundColor: '#fff',
    marginVertical: 10,
    width: width - 32,
    alignSelf: 'center',
    overflow: 'hidden',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center', // Align items at the top to bring userInfo closer to the profile picture
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  userInfoContainer: {
    flex: 1, // This helps take up space to align items correctly
  },
  profilePicContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    overflow: 'hidden',
  },
  profilePic: {
    width: '100%',
    height: '100%',
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  timeAgo: {
    fontSize: 14,
    color: 'gray',
    fontFamily: 'Helvetica Neue',
  },
  titleAndLikeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  postTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    flex: 1, // This allows the title to take up the remaining space
    fontFamily: 'Helvetica Neue',
    alignItems: 'center',
    lineHeight: 18,
  },
  postImage: {
    width: '100%',
    height: 200,
    marginVertical: 10,
    resizeMode: 'cover',
  },
  postBody: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  likeButton: {
    marginTop: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Highlighted Tab Styles
  tabBarIndicator: {
    backgroundColor: '#C3E7F5', // Highlight color for the active tab
    height: 4, // Thickness of the highlight line
  },
  tabBarStyle: {
    backgroundColor: '#fff', // Optional: Customize the background color of the tab bar
  },
  LikeRowText: {

    flex: 1,
   
    width: Dimensions.get('window').width - 60,
  },
});

export default HomeFeedScreen;
