import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import challengeRecommendationData from '../../static/Homefeed/challengeRecommendation.json';

const {width: screenWidth} = Dimensions.get('window');

interface SubCategory {
  Title: string;
}

interface ChallengeCategory {
  MainCategory: string;
  subCategory: SubCategory[];
}

interface ChallengeRecommendationsProps {
  navigation: any;
}

const ChallengeRecommendations: React.FC<ChallengeRecommendationsProps> = ({
  navigation,
}) => {
  const [challengeData, setChallengeData] = useState<ChallengeCategory[]>([]);

  useEffect(() => {
    setChallengeData(challengeRecommendationData.Data);
  }, []);

  const handleSubCategorySelect = (subCategory: string, category: string) => {
    // Navigate directly to challenge creation screen with AI prompt
    navigation.navigate('ChallengeCreationScreenStack', {
      screen: 'ChallengeCreationScreen',
      params: {
        preSelectedCategory: category,
        preSelectedSubCategory: subCategory,
        autoOpenAI: true,
        aiPrompt: subCategory,
      },
    });
  };

  const renderCategoryCard = ({item}: {item: ChallengeCategory}) => (
    <View style={styles.categoryCard}>
      <View style={styles.categoryCardContent}>
        <Text style={styles.categoryTitle}>{item.MainCategory}</Text>
        <View style={styles.subCategoryPreview}>
          {item.subCategory.slice(0, 4).map((subItem, index) => (
            <TouchableOpacity
              key={index}
              style={styles.subCategoryPreviewItem}
              onPress={() => handleSubCategorySelect(subItem.Title.trim(), item.MainCategory)}>
              <Text style={styles.subCategoryPreviewText}>
                {subItem.Title.trim()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );



  return (
    <View style={styles.container}>
      <View style={styles.parentBackground}>
        <View style={styles.headerSection}>
          <Image
            source={require('../../assets/images/challengeBig.png')}
            style={styles.challengeIcon}
            resizeMode={'contain'}
          />
          <Text style={styles.headerText}>Try creating this challenge!</Text>
        </View>

        {/* Horizontal Category Cards */}
        <FlatList
          data={challengeData}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={item => item.MainCategory}
          renderItem={renderCategoryCard}
          style={styles.categoryCardsList}
          contentContainerStyle={styles.categoryCardsContent}
          snapToInterval={screenWidth * 0.75 + 16} // Card width + margin
          decelerationRate="fast"
          snapToAlignment="start"
        />

        {/* Common Create Challenge Button */}
        <TouchableOpacity
          style={[
            styles.createButton,
            !selectedSubCategory && styles.createButtonDisabled,
          ]}
          onPress={handleCreateChallenge}
          disabled={!selectedSubCategory}>
          <Text
            style={[
              styles.createButtonText,
              !selectedSubCategory && styles.createButtonTextDisabled,
            ]}>
            Create Challenge
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    marginTop: 20
  },
  parentBackground: {
    backgroundColor: '#FFC13B',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  challengeIcon: {
    width: 32,
    height: 39,
    marginRight: 12,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000',
    flex: 1,
  },
  categoryCardsList: {
    marginBottom: 0,
  },
  categoryCardsContent: {
    paddingRight: 16,
  },
  categoryCard: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginRight: 16,
    width: screenWidth * 0.75,
    borderWidth: 1,
    borderColor: '#000000',
  },
  categoryCardContent: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 19,
    fontWeight: '700',
    color: '#000',
    textAlign: 'center',
    marginBottom: 16,
  },
  subCategoryPreview: {
    gap: 8,
  },
  subCategoryPreviewItem: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#000000',
  },
  subCategoryPreviewText: {
    fontSize: 14,
    color: '#000',
    textAlign: 'center',
  },
  selectedSubCategoryItem: {
    backgroundColor: '#b9e6fc',
  },
  selectedSubCategoryText: {
    color: '#000',
  },
  createButton: {
    backgroundColor: '#b9e6fc',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#000000',
    marginTop: 16,
  },
  createButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
  createButtonText: {
    fontSize: 16,
    color: '#000',
  },
  createButtonTextDisabled: {
    color: '#666',
  },
});

export default ChallengeRecommendations;
