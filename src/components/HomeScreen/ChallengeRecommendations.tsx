import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import challengeRecommendationData from '../../static/Homefeed/challengeRecommendation.json';

const {width: screenWidth} = Dimensions.get('window');

interface SubCategory {
  Title: string;
}

interface ChallengeCategory {
  MainCategory: string;
  subCategory: SubCategory[];
}

interface ChallengeRecommendationsProps {
  navigation: any;
}

const ChallengeRecommendations: React.FC<ChallengeRecommendationsProps> = ({
  navigation,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | null>(
    null,
  );
  const [challengeData, setChallengeData] = useState<ChallengeCategory[]>([]);

  useEffect(() => {
    setChallengeData(challengeRecommendationData.Data);
  }, []);

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSelectedSubCategory(null); // Reset subcategory when main category changes
  };

  const handleSubCategorySelect = (subCategory: string) => {
    setSelectedSubCategory(subCategory);
  };

  const handleCreateChallenge = () => {
    if (selectedCategory && selectedSubCategory) {
      // Navigate to challenge creation screen with selected data
      navigation.navigate('ChallengeCreationScreenStack', {
        screen: 'ChallengeCreationScreen',
        params: {
          preSelectedCategory: selectedCategory,
          preSelectedSubCategory: selectedSubCategory,
        },
      });
    }
  };

  const renderCategoryItem = ({item}: {item: ChallengeCategory}) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.MainCategory && styles.selectedCategoryItem,
      ]}
      onPress={() => handleCategorySelect(item.MainCategory)}>
      <Text
        style={[
          styles.categoryText,
          selectedCategory === item.MainCategory && styles.selectedCategoryText,
        ]}>
        {item.MainCategory}
      </Text>
    </TouchableOpacity>
  );

  const renderSubCategoryItem = ({item}: {item: SubCategory}) => (
    <TouchableOpacity
      style={[
        styles.subCategoryItem,
        selectedSubCategory === item.Title && styles.selectedSubCategoryItem,
      ]}
      onPress={() => handleSubCategorySelect(item.Title)}>
      <Text
        style={[
          styles.subCategoryText,
          selectedSubCategory === item.Title && styles.selectedSubCategoryText,
        ]}>
        {item.Title}
      </Text>
    </TouchableOpacity>
  );

  const selectedCategoryData = challengeData.find(
    category => category.MainCategory === selectedCategory,
  );

  return (
    <View style={styles.container}>
      <View style={styles.recommendationCard}>
        <View style={styles.headerSection}>
          <Image
            source={require('../../assets/images/challengeBig.png')}
            style={styles.challengeIcon}
          />
          <Text style={styles.headerText}>Try creating this challenge!</Text>
        </View>

        <View style={styles.contentSection}>
          {/* Main Categories Horizontal Scroll */}
          <Text style={styles.sectionTitle}>Categories</Text>
          <FlatList
            data={challengeData}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={item => item.MainCategory}
            renderItem={renderCategoryItem}
            style={styles.categoryList}
            contentContainerStyle={styles.categoryListContent}
          />

          {/* Sub Categories */}
          {selectedCategory && selectedCategoryData && (
            <View style={styles.subCategorySection}>
              <Text style={styles.selectedCategoryTitle}>
                {selectedCategory}
              </Text>
              <FlatList
                data={selectedCategoryData.subCategory}
                keyExtractor={item => item.Title}
                renderItem={renderSubCategoryItem}
                style={styles.subCategoryList}
                scrollEnabled={false}
              />
            </View>
          )}
        </View>

        {/* Create Challenge Button */}
        <TouchableOpacity
          style={[
            styles.createButton,
            (!selectedCategory || !selectedSubCategory) &&
              styles.createButtonDisabled,
          ]}
          onPress={handleCreateChallenge}
          disabled={!selectedCategory || !selectedSubCategory}>
          <Text
            style={[
              styles.createButtonText,
              (!selectedCategory || !selectedSubCategory) &&
                styles.createButtonTextDisabled,
            ]}>
            Create Challenge
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  recommendationCard: {
    backgroundColor: '#FFC947',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  challengeIcon: {
    width: 32,
    height: 32,
    marginRight: 12,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
  },
  contentSection: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  categoryList: {
    marginBottom: 16,
  },
  categoryListContent: {
    paddingRight: 16,
  },
  categoryItem: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 12,
    borderWidth: 2,
    borderColor: '#E0E0E0',
  },
  selectedCategoryItem: {
    backgroundColor: '#87CEEB',
    borderColor: '#5BB3D1',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  selectedCategoryText: {
    color: '#FFF',
    fontWeight: '600',
  },
  subCategorySection: {
    marginTop: 8,
  },
  selectedCategoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 16,
  },
  subCategoryList: {
    maxHeight: 200,
  },
  subCategoryItem: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedSubCategoryItem: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  subCategoryText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  selectedSubCategoryText: {
    color: '#1976D2',
    fontWeight: '600',
  },
  createButton: {
    backgroundColor: '#87CEEB',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFF',
  },
  createButtonTextDisabled: {
    color: '#666',
  },
});

export default ChallengeRecommendations;
