import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import challengeRecommendationData from '../../static/Homefeed/challengeRecommendation.json';

const {width: screenWidth} = Dimensions.get('window');

interface SubCategory {
  Title: string;
}

interface ChallengeCategory {
  MainCategory: string;
  subCategory: SubCategory[];
}

interface ChallengeRecommendationsProps {
  navigation: any;
}

const ChallengeRecommendations: React.FC<ChallengeRecommendationsProps> = ({
  navigation,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | null>(
    null,
  );
  const [challengeData, setChallengeData] = useState<ChallengeCategory[]>([]);

  useEffect(() => {
    setChallengeData(challengeRecommendationData.Data);
  }, []);

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setSelectedSubCategory(null); // Reset subcategory when main category changes
  };

  const handleSubCategorySelect = (subCategory: string) => {
    setSelectedSubCategory(subCategory);
  };

  const handleCreateChallenge = () => {
    if (selectedCategory && selectedSubCategory) {
      // Navigate to challenge creation screen with selected data
      navigation.navigate('ChallengeCreationScreenStack', {
        screen: 'ChallengeCreationScreen',
        params: {
          preSelectedCategory: selectedCategory,
          preSelectedSubCategory: selectedSubCategory,
        },
      });
    }
  };

  const renderCategoryCard = ({item}: {item: ChallengeCategory}) => (
    <View
      style={[
        styles.categoryCard,
        selectedCategory === item.MainCategory && styles.selectedCategoryCard,
      ]}>
      <TouchableOpacity
        style={styles.categoryCardContent}
        onPress={() => handleCategorySelect(item.MainCategory)}>
        <Text style={styles.categoryTitle}>{item.MainCategory}</Text>
        <View style={styles.subCategoryPreview}>
          {item.subCategory.slice(0, 4).map((subItem, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.subCategoryPreviewItem,
                selectedSubCategory === subItem.Title.trim() &&
                  styles.selectedSubCategoryItem,
              ]}
              onPress={() => handleSubCategorySelect(subItem.Title.trim())}>
              <Text
                style={[
                  styles.subCategoryPreviewText,
                  selectedSubCategory === subItem.Title.trim() &&
                    styles.selectedSubCategoryText,
                ]}>
                {subItem.Title.trim()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.cardCreateButton,
          (!selectedCategory || !selectedSubCategory || selectedCategory !== item.MainCategory) &&
            styles.cardCreateButtonDisabled,
        ]}
        onPress={handleCreateChallenge}
        disabled={!selectedCategory || !selectedSubCategory || selectedCategory !== item.MainCategory}>
        <Text
          style={[
            styles.cardCreateButtonText,
            (!selectedCategory || !selectedSubCategory || selectedCategory !== item.MainCategory) &&
              styles.cardCreateButtonTextDisabled,
          ]}>
          Create Challenge
        </Text>
      </TouchableOpacity>
    </View>
  );



  return (
    <View style={styles.container}>
      <View style={styles.headerSection}>
        <Image
          source={require('../../assets/images/challengeBig.png')}
          style={styles.challengeIcon}
        />
        <Text style={styles.headerText}>Try creating this challenge!</Text>
      </View>

      {/* Horizontal Category Cards */}
      <FlatList
        data={challengeData}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.MainCategory}
        renderItem={renderCategoryCard}
        style={styles.categoryCardsList}
        contentContainerStyle={styles.categoryCardsContent}
        snapToInterval={screenWidth * 0.8 + 16} // Card width + margin
        decelerationRate="fast"
        snapToAlignment="start"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  challengeIcon: {
    width: 32,
    height: 32,
    marginRight: 12,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
  },
  categoryCardsList: {
    marginBottom: 16,
  },
  categoryCardsContent: {
    paddingHorizontal: 16,
  },
  categoryCard: {
    backgroundColor: '#FFC947',
    borderRadius: 16,
    padding: 16,
    marginRight: 16,
    width: screenWidth * 0.8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedCategoryCard: {
    backgroundColor: '#FFB347',
    borderWidth: 3,
    borderColor: '#87CEEB',
  },
  categoryCardContent: {
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 16,
  },
  subCategoryPreview: {
    gap: 8,
  },
  subCategoryPreviewItem: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  subCategoryPreviewText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  subCategorySection: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  subCategoryList: {
    maxHeight: 200,
  },
  subCategoryItem: {
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedSubCategoryItem: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  subCategoryText: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
  },
  selectedSubCategoryText: {
    color: '#1976D2',
    fontWeight: '600',
  },
  cardCreateButton: {
    backgroundColor: '#87CEEB',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  cardCreateButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
  cardCreateButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFF',
  },
  cardCreateButtonTextDisabled: {
    color: '#666',
  },
});

export default ChallengeRecommendations;
