import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Linking,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {SignUpStyle} from '../../styles/SignUp';
import acrossAllScreens from '../../styles/acrossAllScreens';

export function TutorialScreen4() {
  const navigation = useNavigation();

  return (
    <View style={[acrossAllScreens.ScreenBackground]}>
      <View style={[acrossAllScreens.ScreenBorders, styles.container]}>
        {/* Header Row: Back Button + Title */}
        <View style={styles.headerRow}>
          <TouchableOpacity
            style={[acrossAllScreens.backImageContainer, styles.backButton]}
            onPress={() => navigation.goBack()}>
            <Image
              style={acrossAllScreens.backImage}
              source={require('../../assets/images/back.png')}
            />
          </TouchableOpacity>
          <Text style={[acrossAllScreens.ScreenHeaderText, styles.header]}>
            It’s easy to create on
          </Text>
        </View>

        <Text style={[acrossAllScreens.ScreenHeaderText, styles.subHeader]}>
          Napoz
        </Text>

        {/* Progress Bar (60%) */}
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, {width: '100%'}]} />
        </View>

        {/* Image Section */}
        <View style={styles.imageRow}>
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen4Img1.jpg')}
          />
          <Image
            style={styles.imageStyle}
            source={require('../../static/Images/TutorialScreen4Img2.jpg')}
          />
        </View>

        {/* Title & Description */}
        <Text style={styles.title}>
          Learn more about the app and its features
        </Text>
        <Text style={styles.description}>
          To learn more about how to use the app and its features, check out
          the tutorial videos under App Tutorials in the Settings section
          or checkout our youtube channel :
        </Text>
  <TouchableOpacity
    style={styles.youtubeLink}
    onPress={() => {
        Linking.openURL('https://www.youtube.com/@Napoz-app');
      }}>
    <AntDesign name="youtube" size={20} color="white" style={{ marginRight: 8 }} />
    <Text style={[acrossAllScreens.H3, { color: 'white', fontWeight: 'bold' }]}>Napoz App</Text>
  </TouchableOpacity>


        {/* Next Button */}
        <View style={styles.bottomButtonContainer}>
          <TouchableOpacity
            style={[SignUpStyle.signupButton]}
            onPress={() => {
              console.log('Home Screen');

              navigation.reset({
                index: 0,
                routes: [{name: 'BottomTabsNavigator', params: {screen: 'Home'}}],
              });
            }}>
            <Text style={SignUpStyle.signUpText}>Start Using the App</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

// Styles
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // Ensures text stays centered
    width: '100%',

    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 0,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subHeader: {
    textAlign: 'center',
    marginBottom: 10,
    fontSize: 22,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    width: '80%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 10,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#B3E5FC',
  },
  imageRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginVertical: 10,
    paddingTop: 10,
    width: '100%',
  },
  imageStyle: {
    width: 180,
    height: 165,
    resizeMode: 'contain',
  },
  imageStyle2: {
    width: 160,
    height: 145,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 0,
    textAlign: 'center',
    // paddingTop: 10,
  },
  description: {
    fontSize: 14,
    textAlign: 'justify',
    paddingHorizontal: 15,
    marginTop: 10,
    // lineHeight: 26,
    includeFontPadding: false, // Fixes text height issues on Android
    allowFontScaling: false, // Disables system font scaling inconsistencies
  },
  description2: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 15,
    paddingBottom: 20,
  },  bottomButtonContainer: {
    position: 'absolute',
    bottom: 32,
    width: '100%',
    alignItems: 'center',
  },
  youtubeLink: {
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#FF0000', // YouTube red
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
  }
});

export default TutorialScreen4;

