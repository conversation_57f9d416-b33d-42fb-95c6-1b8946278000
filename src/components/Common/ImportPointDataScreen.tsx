import React, {useState} from 'react';
import {Text, View, StyleSheet, TouchableOpacity, FlatList,Image} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5'; // Make sure to install this package
import acrossAllScreens from '../../styles/acrossAllScreens';
import WaterSportsIcon from './WaterSportsIcons';

export function ImportPointDataScreen({
  onOptionSelect,
  closeModal,
  ExternalApp
}) {
  const [optionSettings, setOptionSettings] = useState([
    {
      setting: 'Steps',
      description:
        'Track daily steps from your health app',
      image: require('../../assets/images/steps.png'),
    },
    {
      setting: 'Distance',
      description:
        'Track walking and running distance',
      image: require('../../assets/images/distance.png'),
    },
    {
      setting: 'Cycling Distance',
      description:
        'Track cycling distance and routes',
      image: require('../../assets/images/cycling_distance.png'),
    },
    {
      setting: 'Calories',
      description:
        'Track active calories burned',
      image: require('../../assets/images/calories.png'),
    },
    {
      setting: 'Swimming',
      description:
        'Track swimming distance and sessions',
      image: require('../../assets/images/distance.png'), // Using distance icon as placeholder
      iconType: 'swimming',
    },
    {
      setting: 'Paddling',
      description:
        'Track paddling and paddle sports activities',
      image: require('../../assets/images/cycling_distance.png'), // Using cycling icon as placeholder
      iconType: 'paddling',
    },
  ]);
  const handleSelectedOption = selectOption => {
    // Call the callback function passed from the parent component
    onOptionSelect(selectOption.setting);
    // Close the modal
    closeModal();
  };

  const renderItem = ({item}) => (
    <TouchableOpacity
      style={[styles.itemContainer]}
      onPress={() => handleSelectedOption(item)}>
      {item.iconType ? (
        <WaterSportsIcon type={item.iconType} size={30} />
      ) : (
        <Image source={item.image} style={styles.imageSize} />
      )}
      <View style={styles.textContainer}>
        <Text style={styles.setting}>{item.setting}</Text>
        <Text style={styles.description}>{item.description}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <FlatList
      data={optionSettings}
      renderItem={renderItem}
      keyExtractor={item => item.setting}
      style={[styles.list]}
    />
  );
}

const styles = StyleSheet.create({
  list: {
    backgroundColor: 'white',
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 10,
    flex: 1,
  },
  setting: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 12,
    color: '#666',
    flexWrap: 'wrap',
    marginTop: 2,
  },
  imageSize:{
    width: 30,
    height: 30,
    resizeMode: 'contain',
  }
});

export default ImportPointDataScreen;
