import React from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';

interface WaterSportsIconProps {
  type: 'swimming' | 'paddling' | 'watersports';
  size?: number;
  color?: string;
}

export const WaterSportsIcon: React.FC<WaterSportsIconProps> = ({ 
  type, 
  size = 30, 
  color = '#000000' 
}) => {
  const renderIcon = () => {
    switch (type) {
      case 'swimming':
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <FontAwesome5 name="swimmer" size={size * 0.8} color={color} />
          </View>
        );
      case 'paddling':
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <FontAwesome5 name="ship" size={size * 0.8} color={color} />
          </View>
        );
      case 'watersports':
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <Icon name="pool" size={size * 0.8} color={color} />
          </View>
        );
      default:
        return (
          <View style={[styles.iconContainer, { width: size, height: size }]}>
            <Icon name="pool" size={size * 0.8} color={color} />
          </View>
        );
    }
  };

  return renderIcon();
};

const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
  },
});

export default WaterSportsIcon;
