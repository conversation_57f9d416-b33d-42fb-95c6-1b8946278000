import React, {useState} from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  GestureResponderEvent,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
  Switch,
  Platform,
} from 'react-native';
import {useAppDispatch} from '../../redux/Store';
import acrossAllScreens from '../../styles/acrossAllScreens';
import {
  deleteSessionFromAI,
  generateDetailsFromAI,
  reGenerateDetailsFromAI,
} from '../../redux/Challenge/ChallengeAction';
import {Alert} from 'react-native';
import Toast from 'react-native-toast-message';

export type AIGeneratedData = {
  title: string;
  description: string;
  pointTitle: string;
};

type AiGenerationModalProps = {
  visible: boolean;
  onClose: () => void;
  title?: string;
  onConfirm: (data: AIGeneratedData) => void;
  isPost?: boolean;
  challengeTitle?: string | null;
  initialPrompt?: string;
};

const AiGenerationModal: React.FC<AiGenerationModalProps> = ({
  visible,
  onClose,
  title = 'Napoz AI',
  onConfirm,
  isPost = false,
  challengeTitle,
  initialPrompt = '',
}) => {
  const [challengePrompt, setChallengePrompt] = useState(initialPrompt);
  const [generatedTitle, setGeneratedTitle] = useState('');
  const [generatedDesc, setGeneratedDesc] = useState('');
  const [generatedPointTitle, setGeneratedPointTitle] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLong, setIsLong] = useState(false);
  const [hasAutoGenerated, setHasAutoGenerated] = useState(false);
  const dispatch = useAppDispatch();

  // Update prompt when initialPrompt changes
  React.useEffect(() => {
    if (initialPrompt) {
      setChallengePrompt(initialPrompt);
    }
  }, [initialPrompt]);

  const promptText = isPost ? 'Enter Post Prompt' : 'Enter Challenge Prompt';
  const promptPlaceHolder = isPost
    ? 'Describe your post in a few words or a sentence. Napoz AI will give you suggestion for your title and description.'
    : 'Describe your challenge in a few words or a sentence. Napoz AI will give you suggestion for your title, description and in case selected, leaderboard metrics.';
  const titleText = isPost ? 'Post Title?' : 'Challenge Title';
  const descriptionText = isPost
    ? 'Post Description placeholder '
    : 'Challenge Description placeholder';

  const getPostPrompt = () => {
    const length = isLong ? 'long' : 'short';
    if (challengeTitle) {
      return `"${challengeTitle}"; "${challengePrompt}"; ${length}`;
    } else {
      return `;"${challengePrompt}"; ${length}`;
    }
  };

  const deleteSession = async () => {
    if (sessionId) {
      try {
        await dispatch(deleteSessionFromAI()).unwrap();
        console.log('Session deleted successfully');
      } catch (error: any) {
        console.error('Error deleting session:', error);
      }
    }
  };
  

  const onGenerateClick = () => {
    setLoading(true);
    const prompt = isPost ? getPostPrompt() : challengePrompt;
    dispatch(generateDetailsFromAI({prompt: prompt, isPost: isPost}))
      .unwrap()
      .then((data: any) => {
        if (data.sessionId) {
          setSessionId(data.sessionId);
          if (isPost) {
            setGeneratedDesc(data.content.one);
          } else {
            setGeneratedTitle(data.content.one);
            setGeneratedDesc(data.content.two);
            setGeneratedPointTitle(data.content.three);
          }
        }
      })
      .catch((error: any) => {
        if (error.status === 509) {
          Toast.show({
            type: 'error',
            text1: 'Attempts Exceeded! Try again later.',
            text2: '',
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // Auto-generate when modal opens with initial prompt
  React.useEffect(() => {
    if (visible && initialPrompt && initialPrompt.trim() !== '' && !loading && !sessionId && !hasAutoGenerated) {
      console.log('Auto-generating AI content for prompt:', initialPrompt);
      setHasAutoGenerated(true);
      setTimeout(() => {
        onGenerateClick();
      }, 800); // Small delay to ensure modal is fully rendered
    }
  }, [visible, initialPrompt, hasAutoGenerated]);

  // Reset auto-generation flag when modal closes
  React.useEffect(() => {
    if (!visible) {
      setHasAutoGenerated(false);
      setSessionId('');
      setGeneratedTitle('');
      setGeneratedDesc('');
      setGeneratedPointTitle('');
    }
  }, [visible]);

  const onRegenerateClick = (type: string) => {
    if (!sessionId) {
      return;
    }
    setLoading(true);
    dispatch(
      reGenerateDetailsFromAI({
        type: type,
        sessionId: sessionId,
        isPost: isPost,
      }),
    )
      .unwrap()
      .then((data: any) => {
        if (data.sessionId) {
          setSessionId(data.sessionId);
          if (isPost) {
            setGeneratedDesc(data.content.one);
          } else {
            setGeneratedTitle(data.content.one);
            setGeneratedDesc(data.content.two);
            setGeneratedPointTitle(data.content.three);
          }
        }
      })
      .catch((error: any) => {
        if (error.status === 509) {
          Toast.show({
            type: 'error',
            text1: 'Attempts Exceeded! Try again later.',
            text2: '',
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onConfirmClick = () => {
    onConfirm({
      title: generatedTitle,
      description: generatedDesc,
      pointTitle: generatedPointTitle,
    });
  };

  return (
    <Modal
      animationType="slide"
      visible={visible}
      presentationStyle="fullScreen"
      onRequestClose={onClose}>
      <View style={[styles.container, acrossAllScreens.ScreenBorders]}>
        <SafeAreaView style={{flex: 1}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1}}
            showsVerticalScrollIndicator={false}>
            <View style={styles.headerView}>
              <TouchableOpacity
                style={acrossAllScreens.backImageContainer}
                onPress={onClose}>
                <Image
                  style={acrossAllScreens.backImage}
                  source={require('../../assets/images/back.png')}
                />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>{title}</Text>
              <View style={{width: 21}} />
            </View>
            {/* <Text style={[styles.challengePrompt,acrossAllScreens.H2]}>{promptText}</Text> */}
            <TextInput
              placeholder={promptText}
              multiline
              value={challengePrompt}
              onChangeText={text => {
                setChallengePrompt(text);
                setSessionId('');
              }}
              placeholderTextColor={'#7c7c7c'}
              style={[styles.challengePrompt, acrossAllScreens.H2]}
            />
            <Text style={[styles.challengeDesc, acrossAllScreens.H2]}>
              {promptPlaceHolder}
            </Text>
            {isPost && (
              <View style={styles.toggleContainer}>
                <Text style={acrossAllScreens.H2}>Make post Long ?</Text>
                <Switch
                  value={isLong}
                  onValueChange={value => {
                    setIsLong(value);
                    setSessionId('');
                  }}
                  thumbColor={isLong ? '#FFFFFF' : '#C3E7F5'}
                  trackColor={{false: '#CDCDCD', true: '#C3E7F5'}}
                />
              </View>
            )}
            <TouchableOpacity
              style={styles.generateBtn}
              onPress={async () => {
                if (sessionId && (!isPost)) {
                  onRegenerateClick('CHALLENGE_ALL');
                } else if (sessionId && isPost) {
                  await deleteSession();
                  onGenerateClick();
                } else {
                  onGenerateClick();
                }
              }}>
              <Image
                source={require('../../assets/images/NapozAI.png')}
                style={styles.napozAIImage}
              />
              <Text style={styles.btnText}>
                {sessionId ? 'Regenerate' : 'Generate'}
              </Text>
            </TouchableOpacity>
            {(sessionId && generatedDesc) && (
              <View>
                {/* {((isPost && challengeTitle) || !isPost) && ( */}
                {(!isPost) && (
                  <View style={[styles.headerView, {marginBottom: 10}]}>
                    <Text style={styles.titleChallenge}>
                      {generatedTitle ? generatedTitle : titleText}
                    </Text>
                    <TouchableOpacity
                      onPress={() => onRegenerateClick('CHALLENGE_TITLE')}>
                      <Image
                        style={styles.regenerate}
                        source={require('../../assets/images/regenerate.png')}
                      />
                    </TouchableOpacity>
                  </View>
                )}
                <Text style={[styles.challengePrompt, {marginBottom: 3}]}>
                  {generatedDesc ? generatedDesc : descriptionText}
                </Text>
                <TouchableOpacity
                  style={styles.regenarateContainer}
                  onPress={() =>
                    // onRegenerateClick(
                    //   isPost ? 'POST_DESCRIPTION' : 'CHALLENGE_DESCRIPTION',
                    // )
                    {if (sessionId && (!isPost)) {

                      onRegenerateClick(
                        'CHALLENGE_DESCRIPTION',
                      );
                    } else if (sessionId && isPost) {
                      onGenerateClick();
                    } }
                  }>
                  <Image
                    style={styles.regenerate}
                    source={require('../../assets/images/regenerate.png')}
                  />
                </TouchableOpacity>
                {!isPost && (
                  <View style={[styles.headerView, {marginTop: 10}]}>
                    <Text style={acrossAllScreens.H2}>
                      {generatedPointTitle
                        ? generatedPointTitle
                        : 'Leaderboard Points Column Title'}
                    </Text>
                    <TouchableOpacity
                      onPress={() =>
                        onRegenerateClick('CHALLENGE_POINTS_TITLE')
                      }>
                      <Image
                        style={styles.regenerate}
                        source={require('../../assets/images/regenerate.png')}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
          </ScrollView>
          <View style={styles.headerView}>
            <TouchableOpacity
              onPress={async () => {
                await deleteSession();
                setSessionId('');
                onClose();
              }}
              style={styles.discardBtn}>
              <Text style={styles.discardText}>Discard</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onConfirmClick}
              disabled={!sessionId}
              style={[styles.confirmBtn, !sessionId && {opacity: 0.5}]}>
              <Text style={[styles.btnText, {marginLeft: 0}]}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
        {loading && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size={'large'} />
          </View>
        )}
        {visible && (
          <Toast
            position={'bottom'}
            bottomOffset={Platform.OS == 'ios' ? 100 : 60}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 8,
  },
  toggleTitle: {
    fontSize: 14,
    fontWeight: '300',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  regenarateContainer: {
    alignSelf: 'flex-start',
  },
  confirmBtn: {
    flex: 1,
    marginLeft: 8,
    backgroundColor: '#C3E7F5',
    paddingVertical: 20,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  discardBtn: {
    flex: 1,
    marginRight: 8,
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleColumn: {
    fontSize: 14,
    fontWeight: '300',
    color: '#00000080',
    fontFamily: 'Helvetica Neue',
  },
  regenerate: {
    height: 27,
    width: 27,
  },
  titleChallenge: {
    fontSize: 24,
    fontWeight: '500',
    flex: 1,
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  discardText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  btnText: {
    marginLeft: 10,
    fontSize: 18,
    fontWeight: '500',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
  },
  napozAIImage: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginHorizontal: 5,
  },
  generateBtn: {
    backgroundColor: '#C3E7F5',
    paddingHorizontal: 50,
    paddingVertical: 20,
    borderRadius: 4,
    alignItems: 'center',
    alignSelf: 'center',
    flexDirection: 'row',
    marginVertical: 14,
  },
  challengePrompt: {
    // marginTop: 8,
    // fontSize: 14,
    // fontWeight: '300',
    color: '#000000',
    fontFamily: 'Helvetica Neue',
    marginTop: Platform.OS === 'android' ? 2 : 12,
    marginBottom: Platform.OS === 'android' ? 0 : 16
  },
  challengeDesc: {
    // fontSize: 14,
    // fontWeight: '300',
    // color: '#000000',
    // marginTop: 10,
    fontFamily: 'Helvetica Neue',
    marginBottom: 10

  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '500',
    color: 'black',
    fontFamily: 'Helvetica Neue',
  },
  backImage: {
    width: 21,
    height: 21,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Platform.OS === 'android' ? 0 : 16,

    // paddingVertical: 16,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    // paddingHorizontal: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
    marginBottom: 20,
  },
  content: {
    flex: 1,
  },
});

export default AiGenerationModal;
