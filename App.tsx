import 'react-native-gesture-handler';
import React from 'react';
import {persistor, store} from './src/redux/Store';
import {Provider} from 'react-redux';
import {Provider as PaperProvider} from 'react-native-paper';
import BottomTabsNavigator from './src/components/Common/BottomTabsNavigator';
import {NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {CameraScreen} from './src/components/Common/CameraScreen';
import WelcomeStack from './src/components/Common/WelcomeNavigator';
import HomeFeedScreenStack from './src/components/HomeScreen/HomeFeedScreenStack';
import ChallengeCreationScreenStack from './src/components/ChallengeCreation/ChallengeCreationScreenStack';
import LeaderboardCreationScreenStack from './src/components/LeaderBoardCreation/LeaderboardCreationStack';
import LeaderboardHomeScreenStack from './src/components/Leaderboard/LeaderboardHomeScreenStack';
import {<PERSON><PERSON>reaView, StyleSheet, Platform} from 'react-native';
import CreatePostScreenStack from './src/components/Posts/CreatePostScreenStack';
import {PersistGate} from 'redux-persist/integration/react';
import Toast from 'react-native-toast-message';
import SplashScreen from './src/components/Common/SplashScreen';
import Orientation from 'react-native-orientation-locker';
import messaging from '@react-native-firebase/messaging';
import { Dirs, FileSystem } from 'react-native-file-access';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useDeepLinking from './src/utils/useDeepLinking';
import { navigationRef } from './src/utils/NavigationService';
import analytics from "@react-native-firebase/analytics";
import { handleNotificationRedirect, processNotificationPayload } from './src/utils/NotificationService';

const Stack = createStackNavigator();

export function App() {

  React.useEffect(() => {
    analytics().setAnalyticsCollectionEnabled(!__DEV__);
  }, []);

  React.useEffect(() => {
    configureAPNSToken();
    checkAndClearCache();
    setupNotificationListeners();
    Orientation.lockToPortrait();
    return () => Orientation.unlockAllOrientations();
  }, []);
  useDeepLinking(); // Activate deep linking hook

  const checkAndClearCache = async () => {
    try {
      const lastClearDate = await AsyncStorage.getItem('lastCacheClearDate');
      const now = new Date();
      
      if (lastClearDate) {
        const lastDate = new Date(parseInt(lastClearDate, 10));
        const diffInDays = (now.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24);
  
        if (diffInDays > 2) {
          console.log('Cache clear not needed');
          return;
        }
      }
  
      await clearCacheMemory();
      await AsyncStorage.setItem('lastCacheClearDate', now.getTime().toString());
    } catch (error) {
      console.error('Error checking cache:', error);
    }
  };

  const clearCacheMemory = async () => {
    try {
      const cacheDir = Dirs.CacheDir;
      const files = await FileSystem.ls(cacheDir); // List files in cache
  
      await Promise.all(files.map(file => FileSystem.unlink(`${cacheDir}/${file}`)));
  
      console.log('Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  async function configureAPNSToken() {
    const apnsToken = await messaging().getAPNSToken();
    console.log('APNs Token:', apnsToken);
    if (Platform.OS === 'ios' && apnsToken) {
      messaging().setAPNSToken(apnsToken);
    }
  }

  const setupNotificationListeners = () => {
    // Handle notification when app is opened from a notification (app was closed/background)
    messaging().getInitialNotification().then(remoteMessage => {
      if (remoteMessage) {
        console.log('Notification caused app to open from quit state:', remoteMessage);
        const notificationData = processNotificationPayload(remoteMessage);
        // Delay navigation to ensure app is fully loaded
        setTimeout(() => {
          handleNotificationRedirect(notificationData);
        }, 2000);
      }
    });

    // Handle notification when app is opened from a notification (app was in background)
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('Notification caused app to open from background state:', remoteMessage);
      const notificationData = processNotificationPayload(remoteMessage);
      handleNotificationRedirect(notificationData);
    });

    // Handle notification when app is in foreground
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived in foreground!', remoteMessage);

      // Show a toast or alert for foreground notifications
      if (remoteMessage.notification) {
        Toast.show({
          type: 'info',
          text1: remoteMessage.notification.title || 'New Notification',
          text2: remoteMessage.notification.body || '',
          onPress: () => {
            const notificationData = processNotificationPayload(remoteMessage);
            handleNotificationRedirect(notificationData);
            Toast.hide();
          },
        });
      }
    });

    return unsubscribe;
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <PaperProvider>
          <NavigationContainer ref={navigationRef}>
              <Stack.Navigator
                screenOptions={{
                  headerShown: false,
                  gestureEnabled: false,
                }}>
                <Stack.Screen name="SplashScreen" component={SplashScreen} />
                <Stack.Screen name="WelcomeStack" component={WelcomeStack} />
                <Stack.Screen
                  name="BottomTabsNavigator"
                  component={BottomTabsNavigator}
                />

                <Stack.Screen
                  name="HomeStack"
                  component={HomeFeedScreenStack}
                />
                <Stack.Screen
                  name="ChallengeCreationScreenStack"
                  component={ChallengeCreationScreenStack}
                />
                <Stack.Screen
                  name="CreatePostScreenStack"
                  component={CreatePostScreenStack}
                />
                <Stack.Screen name="CameraScreen" component={CameraScreen} />
                <Stack.Screen
                  name="LeaderboardCreationScreenStack"
                  component={LeaderboardCreationScreenStack}
                />
                <Stack.Screen
                  name="LeaderboardHomeScreenStack"
                  component={LeaderboardHomeScreenStack}
                />
              </Stack.Navigator>
            </NavigationContainer>
          </PaperProvider>
        </PersistGate>
      </Provider>
      <Toast topOffset={Platform.OS == 'ios' ? 80 : 40} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default App;
