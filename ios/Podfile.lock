PODS:
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.16)
  - FBReactNativeSpec (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.16)
    - RCTTypeSafety (= 0.71.16)
    - React-Core (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - ffmpeg-kit-react-native (6.0.2):
    - ffmpeg-kit-react-native/https (= 6.0.2)
    - React-Core
  - ffmpeg-kit-react-native/https (6.0.2):
    - hpsurani-ffmpeg-kit-ios-https (= 6.0.2)
    - React-Core
  - Firebase/Analytics (11.7.0):
    - Firebase/Core
  - Firebase/Core (11.7.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.7.0)
  - Firebase/CoreOnly (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - Firebase/Crashlytics (11.7.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.7.0)
  - Firebase/Messaging (11.7.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.7.0)
  - FirebaseAnalytics (11.7.0):
    - FirebaseAnalytics/AdIdSupport (= 11.7.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.7.0):
    - FirebaseCoreInternal (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - FirebaseCoreInternal (11.7.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (11.7.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.7.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.7.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.71.16):
    - hermes-engine/Pre-built (= 0.71.16)
  - hermes-engine/Pre-built (0.71.16)
  - hpsurani-ffmpeg-kit-ios-https (6.0.2)
  - KTVCocoaHTTPServer (1.0.0):
    - CocoaAsyncSocket
  - KTVHTTPCache (2.0.1):
    - KTVCocoaHTTPServer
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.71.16)
  - RCTTypeSafety (0.71.16):
    - FBLazyVector (= 0.71.16)
    - RCTRequired (= 0.71.16)
    - React-Core (= 0.71.16)
  - React (0.71.16):
    - React-Core (= 0.71.16)
    - React-Core/DevSupport (= 0.71.16)
    - React-Core/RCTWebSocket (= 0.71.16)
    - React-RCTActionSheet (= 0.71.16)
    - React-RCTAnimation (= 0.71.16)
    - React-RCTBlob (= 0.71.16)
    - React-RCTImage (= 0.71.16)
    - React-RCTLinking (= 0.71.16)
    - React-RCTNetwork (= 0.71.16)
    - React-RCTSettings (= 0.71.16)
    - React-RCTText (= 0.71.16)
    - React-RCTVibration (= 0.71.16)
  - React-callinvoker (0.71.16)
  - React-Codegen (0.71.16):
    - FBReactNativeSpec
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.16)
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/Default (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/DevSupport (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.16)
    - React-Core/RCTWebSocket (= 0.71.16)
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-jsinspector (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-Core/RCTWebSocket (0.71.16):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.16)
    - React-cxxreact (= 0.71.16)
    - React-hermes
    - React-jsi (= 0.71.16)
    - React-jsiexecutor (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - Yoga
  - React-CoreModules (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.16)
    - React-Codegen (= 0.71.16)
    - React-Core/CoreModulesHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-RCTBlob
    - React-RCTImage (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-cxxreact (0.71.16):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-jsinspector (= 0.71.16)
    - React-logger (= 0.71.16)
    - React-perflogger (= 0.71.16)
    - React-runtimeexecutor (= 0.71.16)
  - React-hermes (0.71.16):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.71.16)
    - React-jsi
    - React-jsiexecutor (= 0.71.16)
    - React-jsinspector (= 0.71.16)
    - React-perflogger (= 0.71.16)
  - React-jsi (0.71.16):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.16):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-perflogger (= 0.71.16)
  - React-jsinspector (0.71.16)
  - React-logger (0.71.16):
    - glog
  - react-native-cameraroll (5.10.0):
    - React-Core
  - react-native-date-picker (5.0.7):
    - React-Core
  - react-native-image-picker (7.1.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-orientation-locker (1.7.0):
    - React-Core
  - react-native-pager-view (6.4.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-safe-area-context (4.12.0):
    - React-Core
  - react-native-simple-toast (1.1.4):
    - React-Core
    - Toast (~> 4.0.0)
  - react-native-skia (1.12.3):
    - RCT-Folly (= 2021.07.22.00)
    - React
    - React-callinvoker
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video-cache (2.7.4):
    - KTVHTTPCache (~> 2.0.0)
    - React
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (13.12.5):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-perflogger (0.71.16)
  - React-RCTActionSheet (0.71.16):
    - React-Core/RCTActionSheetHeaders (= 0.71.16)
  - React-RCTAnimation (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.16)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTAnimationHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTAppDelegate (0.71.16):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.16):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTBlobHeaders (= 0.71.16)
    - React-Core/RCTWebSocket (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-RCTNetwork (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTImage (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.16)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTImageHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-RCTNetwork (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTLinking (0.71.16):
    - React-Codegen (= 0.71.16)
    - React-Core/RCTLinkingHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTNetwork (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.16)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTNetworkHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTSettings (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.16)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTSettingsHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-RCTText (0.71.16):
    - React-Core/RCTTextHeaders (= 0.71.16)
  - React-RCTVibration (0.71.16):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.16)
    - React-Core/RCTVibrationHeaders (= 0.71.16)
    - React-jsi (= 0.71.16)
    - ReactCommon/turbomodule/core (= 0.71.16)
  - React-runtimeexecutor (0.71.16):
    - React-jsi (= 0.71.16)
  - ReactCommon/turbomodule/bridging (0.71.16):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.16)
    - React-Core (= 0.71.16)
    - React-cxxreact (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-logger (= 0.71.16)
    - React-perflogger (= 0.71.16)
  - ReactCommon/turbomodule/core (0.71.16):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.16)
    - React-Core (= 0.71.16)
    - React-cxxreact (= 0.71.16)
    - React-jsi (= 0.71.16)
    - React-logger (= 0.71.16)
    - React-perflogger (= 0.71.16)
  - ReactNativeFileAccess (3.1.1):
    - React-Core
    - ZIPFoundation
  - RNAppleHealthKit (1.7.0):
    - React
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCPicker (2.9.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (21.7.1):
    - Firebase/Analytics (= 11.7.0)
    - React-Core
    - RNFBApp
  - RNFBApp (21.7.1):
    - Firebase/CoreOnly (= 11.7.0)
    - React-Core
  - RNFBCrashlytics (21.7.1):
    - Firebase/Crashlytics (= 11.7.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBMessaging (21.7.1):
    - Firebase/Messaging (= 11.7.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFlashList (1.7.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.20.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNImageCropPicker (0.42.0):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.42.0)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.42.0):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNLocalize (3.4.1):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.35.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNVectorIcons (9.2.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - Toast (4.0.0)
  - TOCropViewController (2.7.4)
  - VisionCamera (4.2.0):
    - VisionCamera/Core (= 4.2.0)
    - VisionCamera/React (= 4.2.0)
  - VisionCamera/Core (4.2.0)
  - VisionCamera/React (4.2.0):
    - React-Core
  - Yoga (1.14.0)
  - ZIPFoundation (0.9.19)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - ffmpeg-kit-react-native (from `../node_modules/ffmpeg-kit-react-native`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - hpsurani-ffmpeg-kit-ios-https (from `https://raw.githubusercontent.com/HpSurani1997/ffmpeg/master/hpsurani-ffmpeg-kit-ios-https.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-simple-toast (from `../node_modules/react-native-simple-toast`)
  - "react-native-skia (from `../node_modules/@shopify/react-native-skia`)"
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-video-cache (from `../node_modules/react-native-video-cache`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeFileAccess (from `../node_modules/react-native-file-access`)
  - RNAppleHealthKit (from `../node_modules/react-native-health`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - KTVCocoaHTTPServer
    - KTVHTTPCache
    - libevent
    - libwebp
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Toast
    - TOCropViewController
    - ZIPFoundation

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  ffmpeg-kit-react-native:
    :path: "../node_modules/ffmpeg-kit-react-native"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  hpsurani-ffmpeg-kit-ios-https:
    :podspec: https://raw.githubusercontent.com/HpSurani1997/ffmpeg/master/hpsurani-ffmpeg-kit-ios-https.podspec
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-simple-toast:
    :path: "../node_modules/react-native-simple-toast"
  react-native-skia:
    :path: "../node_modules/@shopify/react-native-skia"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-video-cache:
    :path: "../node_modules/react-native-video-cache"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeFileAccess:
    :path: "../node_modules/react-native-file-access"
  RNAppleHealthKit:
    :path: "../node_modules/react-native-health"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 9840513ec2766e31fb9e34c2dabb2c4671400fcd
  FBReactNativeSpec: 76141e46f67b395d7d4e94925dfeba0dbd680ba1
  ffmpeg-kit-react-native: 0866cd9fd26204e7c8a02eb877e26872031f5d92
  Firebase: a64bf6a8546e6eab54f1c715cd6151f39d2329f4
  FirebaseAnalytics: bc9e565af9044ba8d6c6e4157e4edca8e5fdf7ec
  FirebaseCore: 3227e35f4197a924206fbcdc0349325baf4f5de4
  FirebaseCoreExtension: 206c1b399f0d103055207c16f299b28e3dbd1949
  FirebaseCoreInternal: d6c17dafc8dc33614733a8b52df78fcb4394c881
  FirebaseCrashlytics: 785a73b624715bbc09a40bb56cdc3829a801cc98
  FirebaseInstallations: 9347e719c3d52d8d7b9074b2c32407dd027305e9
  FirebaseMessaging: 00ece041b71ddb52a2862ffdee73fb6e9824bd0c
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: 32ed7a9387ae71efe3a35a7f20f3a7292950957b
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: 0471a5b5bff51f3a91b1e76df22c952d04c63967
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  hermes-engine: 2382506846564caf4152c45390dc24f08fce7057
  hpsurani-ffmpeg-kit-ios-https: 4b581fde0ec1b3a7a48ef1c90840bbc8c99f939a
  KTVCocoaHTTPServer: df8d7b861e603ff8037e9b2138aca2563a6b768d
  KTVHTTPCache: 588c3eb16f6bd1e6fde1e230dabfb7bd4e490a4d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 44a3cda52ccac0be738fbf43fef90f3546a48c52
  RCTTypeSafety: da7fbf9826fc898ca8b10dc840f2685562039a64
  React: defd955b6d6ffb9791bb66dee08d90b87a8e2c0c
  React-callinvoker: 39ea57213d56ec9c527d51bd0dfb45cbb12ef447
  React-Codegen: a383556237715e2f75fb0678a932bc5ad53995a5
  React-Core: d28fd78dc9ba11686213ef1304b876fbe14504b0
  React-CoreModules: f1e28e36e71add156b108ff1dd00cfdb5399da68
  React-cxxreact: e0f18fd5ccd178950aeaca8e5e71bea4c1854f69
  React-hermes: 82799e534d3329c041d7b736ea201e7b95da1112
  React-jsi: 4f0c076e37f6c6b9e1ebf5783918a2b3de3697f7
  React-jsiexecutor: 173c86f9ab3434c9134ade7294f8be06398b4f0a
  React-jsinspector: b3b341764ccda14f3659c00a9bc5b098b334db2b
  React-logger: 9fce62c1d7893429ce7558b9f6b83c5c79f946d1
  react-native-cameraroll: 9c9027acae394d1248b7255bbc1cd8fbddd7ba8f
  react-native-date-picker: 585252087d4820b4cd8f2cf80068f6e8f5b72413
  react-native-image-picker: f02b573847436881b851f74b7afbf26a0b0107aa
  react-native-orientation-locker: cc6f357b289a2e0dd2210fea0c52cb8e0727fdaa
  react-native-pager-view: 133c83c04d27ae21a73079a176465bd885ec052b
  react-native-safe-area-context: 8b8404e70b0cbf2a56428a17017c14c1dcc16448
  react-native-simple-toast: 59d8b58998dac9f063b46d95f9fd7fd7caab6101
  react-native-skia: 9b00acafe857965a0903cd7c7c4f45aa56486e0f
  react-native-video: 2aad0d963bf3952bd9ebb2f53fab799338e8e202
  react-native-video-cache: 03aa1f85f515d6db4366689bbd2e7ff068a3e434
  react-native-webview: 12a8113aa7d07e8be424ac23046b6b852f1d5823
  React-perflogger: c944b06edad34f5ecded0f29a6e66290a005d365
  React-RCTActionSheet: fa467f37777dacba2c72da4be7ae065da4482d7d
  React-RCTAnimation: 0591ee5f9e3d8c864a0937edea2165fe968e7099
  React-RCTAppDelegate: 04d2661dee11a68f5fd32c4b5d7ffa0dc0721094
  React-RCTBlob: 8f263e84a89652c58899d2444c2a915aa5057feb
  React-RCTImage: 6300660ef04d0e8a710ad9ea5d2fb4d9521c200d
  React-RCTLinking: 7703ee1b10d3568c143a489ae74adc419c3f3ef3
  React-RCTNetwork: 5748c647e09c66b918f81ae15ed7474327f653be
  React-RCTSettings: 8c8a84ee363db9cbc287c8b8f2fb782acf7ba414
  React-RCTText: d5e53c0741e3e2df91317781e993b5e42bd70448
  React-RCTVibration: 052dd488ba95f461a37c992b9e01bd4bcc59a7b6
  React-runtimeexecutor: b5abe02558421897cd9f73d4f4b6adb4bc297083
  ReactCommon: 478e662e4588567133325322bb1d7adadd9e06bc
  ReactNativeFileAccess: 921d1223be7b739158410e6bc7199b4a9463a261
  RNAppleHealthKit: 86ef7ab70f762b802f5c5289372de360cca701f9
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNCPicker: 0e683a85cae99a8a739e97a49f269a4630de0a01
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFBAnalytics: 3eb2762225fa1a1b04c81c31ea27755b34d3d171
  RNFBApp: 32f7425af42aef99472dd46e14222cb0a8a17504
  RNFBCrashlytics: a0c423ed77d71c8105e5961c357a89075b049fb6
  RNFBMessaging: 993c913f7a037104b5ae02cadf7fc191545ceb51
  RNFlashList: 62f8cb91dd5fbe4f91083c769f69c0eb204fc8c6
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: f12dbf1a6455ee3c99b607f23405be786fe10b42
  RNImageCropPicker: b0afedeefd8655254a15c34103718455fde6ce59
  RNLocalize: 15463c4d79c7da45230064b4adcf5e9bb984667e
  RNPermissions: c366da028e7b2f2088ad7d6ab359662cd5caceac
  RNReanimated: f9f8945aad05d259324869fb79ce30b75c6062da
  RNScreens: a568946532d3cabcb7f46fdc0a8dbada3dd5de44
  RNVectorIcons: 5784330be9dddb5474e8b378d5f6947996c84e55
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  VisionCamera: 5eded672d6f1a6b06dc0c056b9e2dffc5a48ec94
  Yoga: e29645ec5a66fb00934fad85338742d1c247d4cb
  ZIPFoundation: b8c29ea7ae353b309bc810586181fd073cb3312c

PODFILE CHECKSUM: 565fa7ab551d8cff2c2fdae74817946f541cb7c3

COCOAPODS: 1.15.2
