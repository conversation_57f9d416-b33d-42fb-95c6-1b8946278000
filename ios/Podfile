require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '14.0'
install! 'cocoapods', :deterministic_uuids => false

production = ENV["PRODUCTION"] == "1"

target 'AwesomeProject' do
  pod 'hpsurani-ffmpeg-kit-ios-https', :podspec => 'https://raw.githubusercontent.com/HpSurani1997/ffmpeg/master/hpsurani-ffmpeg-kit-ios-https.podspec'
  pod 'ffmpeg-kit-react-native', :path => '../node_modules/ffmpeg-kit-react-native'

  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()
  use_frameworks! :linkage => :static
  $RNFirebaseAsStaticFramework = true
  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :production => production,
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # :flipper_configuration => FlipperConfiguration.enabled,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'AwesomeProjectTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    react_native_post_install(installer)
  
    # NOTE: Change IPHONEOS_DEPLOYMENT_TARGET to 12.4.
    installer.pods_project.targets.each do |target|
      if target.name == 'Flipper'
        file_path = 'Pods/Flipper/xplat/Flipper/FlipperTransportTypes.h'
        contents = File.read(file_path)
        unless contents.include?('#include <functional>')
          File.chmod(0755, file_path)
          File.open(file_path, 'w') do |file|
            file.puts('#include <functional>')
            file.puts(contents)
          end
        end
      end
  
      target.build_configurations.each do |config|
        # Set minimum deployment target
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.4'
  
        # Disable Bitcode
        config.build_settings['ENABLE_BITCODE'] = 'NO'
  
        # Exclude ARM64 for Simulator (Fixes issues on M1 Macs)
        # config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      end
    end
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
  
end
